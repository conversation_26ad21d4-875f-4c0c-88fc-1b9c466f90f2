import { phoneSuadiSchema } from '@/lib/schema'
import { object, string } from 'yup'

export const registerSchema = () => {
  return object({
    name: string().required(),
    phone: phoneSuadiSchema(false),
    email: string().email().optional(),
  })
}

export const loginSchema = () => {
  return object({
    phone: phoneSuadiSchema(false),
  })
}

export const verifyResetCodeSchema = () => {
  return object({
    otp: string().required().matches(/^\d+$/),
  })
}
