import { array, mixed, object, string } from 'yup'

export const schema = (t: any, userData?: IProfile) => {
  return object({
    name: string().required(),
    phone: string().required(),
    username: string()
      .required()
      .min(3)
      .max(20)
      .matches(/^[a-zA-Z0-9_-]+$/),
    id_number: string()
      .matches(/^[0-9]+$/)
      .length(10)
      .required(),
    nationality_id: string().required(),
    location: string().required(),
    bank_name: string().max(100).required(),
    bank_account_number: string()
      .matches(/^[0-9]+$/)
      .min(10)
      .max(14)
      .required(),
    iban: string()
      .matches(/^[A-Za-z0-9]+$/)
      .length(24)
      .test('starts-with-country-code', t('validations.invalid_iban_format'), (value) => {
        if (!value) return false
        return /^[A-Z]{2}/.test(value)
      })
      .required(),
    specializations: array()
      .of(string())
      .max(3, t('validations.specializations_max', { name: t('label.specializations'), max: 3 }))
      .required(),
    image: mixed()
      .test('required', t('validations.image_required'), (value: any) => {
        if (userData?.image) return true
        return !!value
      })
      .test('fileType', t('validations.invalid_image_format'), (value: any) => {
        if (!value && userData?.image) return true
        if (!value) return true
        return ['image/jpeg', 'image/png'].includes(value?.type)
      })
      .test('fileSize', t('validations.image_size_exceeds_5mb'), (value: any) => {
        if (!value && userData?.image) return true
        if (!value) return true
        return value.size <= 5 * 1024 * 1024
      }),
  })
}
