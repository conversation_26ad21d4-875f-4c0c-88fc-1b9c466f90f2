import { IFormWrapper } from '@/components/core/FormWrapper'
import useApi from '@/hooks/useApi'
import { useLocalizedRouter } from '@/hooks/useLocalizedRouter'
import { Routes } from '@/routes/routes'
import { IVerifyResetCode, PhoneData } from '@/types'
import { createFormData } from '@/utils/createFormData'
import { observer } from '@/utils/observer'
import { deleteCookie, getCookie, setCookie } from 'cookies-next'
import { signIn } from 'next-auth/react'
import { useTranslations } from 'next-intl'
import { useEffect, useRef, useState } from 'react'
import { toast } from 'sonner'

const defaultValues: IVerifyResetCode = {
  identifier: '',
  country_code: '966',
  otp: '',
  device_id: '',
  firebase_token: '',
}

const useVerifyResetCode = ({
  type,
  onClose,
  openDialog,
  isCounterActive,
}: {
  type: 'login' | 'register'
  onClose: () => void
  openDialog: boolean
  isCounterActive: boolean
}) => {
  const t = useTranslations()

  const { push } = useLocalizedRouter()

  const [isOpenSuccesDialog, setIsOpenSuccesDialog] = useState(false)
  const [isPending, setIsPending] = useState(false)
  const [phone, setPhone] = useState<PhoneData | undefined>(undefined)

  useEffect(() => {
    const phoneCookie = getCookie('phone')
    console.log('Cookie value:', phoneCookie, 'Type:', typeof phoneCookie)

    // Check if cookie exists and is not undefined/null/invalid
    if (phoneCookie && phoneCookie !== 'undefined' && phoneCookie !== 'null' && typeof phoneCookie === 'string') {
      try {
        const parsedPhone = JSON.parse(phoneCookie) as PhoneData
        console.log('Parsed phone:', parsedPhone)
        setPhone(parsedPhone)
      } catch (error) {
        console.error('Error parsing phone cookie:', error)
        setPhone(undefined)
      }
    } else {
      console.log('Phone cookie is invalid or missing')
      setPhone(undefined)
    }
  }, [openDialog]) // Only run when dialog opens

  const handleCloseSuccessDialog = () => {
    setIsOpenSuccesDialog(false)
    push(Routes.HOME)
  }

  const formRef = useRef<IFormWrapper>(null)
  const [isAllowed, setIsAllowed] = useState(false)

  const fcmToken = getCookie('fcm_token') || ''
  const deviceId = getCookie('device_id') || ''

  const { action: allowedToSendResetCode } = useApi({
    path: '/user/auth/can-send-otp',
    method: 'POST',
    handleSuccess: false,
    onSuccess: (state) => {
      setIsAllowed(state.data.data)
    },
  })

  const onSubmit = async (payload: IVerifyResetCode) => {
    try {
      setIsPending(true)

      // Let NextAuth handle everything - single API call through credentials provider
      const result = await signIn('credentials', {
        redirect: false,
        identifier: phone?.identifier,
        country_code: phone?.country_code,
        otp: payload.otp,
        device_id: deviceId,
        firebase_token: fcmToken,
      })

      if (result?.ok) {
        // Success - session established
        toast.success(t('auth.code_verified_successfully'))
        onClose()

        if (type === 'login') {
          push(Routes.HOME)
        } else {
          setIsOpenSuccesDialog(true)
        }

        deleteCookie('phone')
      } else {
        // Handle authentication errors from NextAuth
        const errorMessage = result?.error || t('auth.invalid_or_expired_otp')
        toast.error(errorMessage)
      }
    } catch (error) {
      console.error('Verification error:', error)
      toast.error(t('auth.unexpected_error'))
    } finally {
      setIsPending(false)
    }
  }

  const { action, isPending: isisPendingResendCode } = useApi({
    path: '/user/auth/otp/send',
    method: 'POST',
    handleSuccess: false,
    onSuccess: (data) => {
      // Store phone data for OTP verification
      if (data?.phone) {
        setCookie('phone', JSON.stringify(data.phone))
      }
      toast.success(t('auth.resend_code_success'))
      // Reset the counter when resend is successful
      observer.fire('handelReCounter')
    },
  })

  const handleResendCode = () => {
    if (!phone?.identifier) {
      toast.error(t('auth.phone_required'))
      return
    }
    const formdata = createFormData(phone)
    action(formdata)
  }

  useEffect(() => {
    if (!phone?.identifier) return // Don't make API call if no phone data

    const formdata = new FormData()
    formdata.append('identifier', phone.identifier)
    formdata.append('country_code', phone.country_code)

    if (openDialog && !isCounterActive) {
      allowedToSendResetCode(formdata)
    }
  }, [openDialog, isCounterActive, phone?.identifier, phone?.country_code])

  return {
    t,
    phone,
    isPending,
    isisPendingResendCode,
    onSubmit,
    defaultValues,
    handleResendCode,
    formRef,
    isAllowed,
    setIsAllowed,
    isOpenSuccesDialog,
    setIsOpenSuccesDialog,
    handleCloseSuccessDialog,
  }
}

export default useVerifyResetCode
