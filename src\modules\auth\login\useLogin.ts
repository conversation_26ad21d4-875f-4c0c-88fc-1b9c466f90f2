import useApi from '@/hooks/useApi'
import { ILogin, PhoneData } from '@/types'
import { createFormData } from '@/utils/createFormData'
import { observer } from '@/utils/observer'
import { setCookie } from 'cookies-next'
import { useTranslations } from 'next-intl'
import { useState, useEffect } from 'react'
import { toast } from 'sonner'

const useLogin = () => {
  const t = useTranslations()
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [phoneData, setPhoneData] = useState<PhoneData | null>(null)
  const [shouldStartCounter, setShouldStartCounter] = useState(false)

  const handleDialogOpen = () => {
    setIsDialogOpen(true)
  }

  const handleDialogClose = () => {
    setIsDialogOpen(false)
  }

  const { action, isPending } = useApi({
    path: '/user/auth/otp/send',
    method: 'POST',
    handleSuccess: false,
    onSuccess: () => {
      // Store phone data for OTP verification
      if (phoneData) {
        setCookie('phone', JSON.stringify(phoneData))
      }
      toast.success(t('auth.send_code_success'))
      handleDialogOpen()
      observer.fire('handelReCounter')
    },
  })

  const handleSubmit = (payload: ILogin) => {
    const formdata = createFormData(payload.phone)
    setPhoneData(payload.phone)
    action(formdata)
  }

  return {
    t,
    isPending,
    handleSubmit,
    handleDialogOpen,
    handleDialogClose,
    isDialogOpen,
  }
}

export default useLogin
