// ** Third Party Imports
import { AuthOptions, getServerSession } from 'next-auth'
import CredentialsProvider from 'next-auth/providers/credentials'

import { env } from '@/config/environment'
import { actionService, setLocaleCookie } from '@/services'
import { createFormData } from '@/utils/createFormData'

const expires = 60 * 60 // ** 1hour default

export const authOptions: AuthOptions = {
  // JWT Secret from env variable
  secret: env.NEXT_PUBLIC_SECRET,

  providers: [
    CredentialsProvider({
      id: 'credentials',
      name: 'Credentials',
      type: 'credentials',
      credentials: {},
      async authorize(credentials: any) {
        try {
          // Convert credentials to FormData for backend
          const formdata = createFormData(credentials)
          const res: any = await actionService(
            {
              path: '/user/auth/otp/verify',
              method: 'POST',
            },
            null,
            formdata
          )

          const user = res?.data?.data

          // Set locale if available
          if (user?.data?.app_locale) {
            setLocaleCookie(user.data.app_locale)
          }

          // Check for successful response
          if (res.status && user) {
            return user
          }

          // Handle API error responses
          const errorMessage = res?.message || res?.error || 'Authentication failed'
          throw new Error(errorMessage)
        } catch (err: any) {
          console.error('Authorization error:', err)

          // Extract error message from various possible sources
          const errorMessage =
            err?.message || err?.response?.data?.message || err?.response?.data?.Error || 'Authentication failed'

          // Throw error with proper message that will be returned to client
          throw new Error(errorMessage)
        }
      },
    }),
  ],

  session: {
    strategy: 'jwt' as const,
    maxAge: expires, // ** 1 hour
  },

  pages: {
    signIn: '/auth/login',
    signOut: '/',
    error: '/auth/login',
  },

  callbacks: {
    async jwt({ token, user, session, trigger }: any) {
      if (user) {
        /*
         * For adding custom parameters to user in session, we first need to add those parameters
         * in token which then will be available in the `session()` callback
         */
        token.user = user
      }

      if (trigger === 'update') {
        token.user = session
      }

      // The Token takes it's value from user data when logged in. If the session has been modified, We have to update the token to match the session value.
      return token
    },
    async session({ session, token }) {
      if (session.user) {
        // ** Add custom params to user in session which are added in `jwt()` callback via `token` parameter
        session.user = token.user
      }

      return session
    },
  },
}

export const getServerAuthSession = async () => {
  return await getServerSession(authOptions)
}
