import { useEffect, useState, useCallback } from 'react'

import { observer } from '@/utils/observer'

export const useCounter = () => {
  const initialCounter = 60
  const [counter, setCounter] = useState<number>(0) // Start at 0, will be set to 60 when triggered
  const [isActive, setIsActive] = useState<boolean>(false)

  const handelReCounter = useCallback(() => {
    setCounter(initialCounter)
    setIsActive(true)
  }, [initialCounter])

  // Subscribe to observer only once on mount
  useEffect(() => {
    observer.subscribe('handelReCounter', handelReCounter)

    return () => {
      observer.unsubscribe('handelReCounter')
    }
  }, [handelReCounter])

  // Handle counter countdown
  useEffect(() => {
    if (!isActive || counter === 0) {
      if (counter === 0 && isActive) {
        observer.fire('changeStateCounterFinally')
        setIsActive(false)
      }
      return
    }

    const counterTime = setTimeout(() => {
      setCounter((prevCounter) => {
        const newCounter = prevCounter - 1
        return newCounter
      })
    }, 1000)

    return () => {
      clearTimeout(counterTime)
    }
  }, [counter, isActive])

  return {
    counter,
    isCounterActive: isActive && counter > 0,
  }
}
