import { useEffect, useState } from 'react'

/**
 * Custom hook to detect if component has mounted on client-side.
 * Prevents hydration mismatches by ensuring SSR and client render the same initial content.
 *
 * @returns {boolean} - Returns true after component has mounted on client, false during SSR
 *
 * @example
 * const MyComponent = () => {
 *   const hasMounted = useHasMounted()
 *
 *   if (!hasMounted) return <Skeleton />
 *
 *   return <ClientOnlyContent />
 * }
 */
export const useHasMounted = (): boolean => {
  const [hasMounted, setHasMounted] = useState(false)

  useEffect(() => {
    setHasMounted(true)
  }, [])

  return hasMounted
}
