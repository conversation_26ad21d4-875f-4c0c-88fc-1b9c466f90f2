'use client'

// next-intl
import { useTranslations } from 'next-intl'

// ui components
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog'

import { verifyResetCodeSchema } from '../schema'
import Counter from './components/Counter'
import useVerifyResetCode from './hooks/useVerifyResetCode'
import HeaderPage from '../components/HeaderPage'
import { Button } from '@/components/ui/button'
import { FormWrapper } from '@/components/core/FormWrapper'
import { FormInputOTP } from '@/components/form/FormInputOTP'
import { useCounter } from './hooks/useCounter'
import CustomDialog from '@/components/shared/CustomDialog'

// images
import SuccessCheck from '/public/icons/tick_circle.svg'
import Image from 'next/image'

type VerifyOtpDialogProps = {
  openDialog: boolean
  onClose: () => void
  type: 'login' | 'register'
}
export default function VerifyOtpDialog({ openDialog, onClose, type }: VerifyOtpDialogProps) {
  const t = useTranslations()

  const { counter, isCounterActive } = useCounter()
  const {
    defaultValues,
    onSubmit,
    handleResendCode,
    formRef,
    isPending,
    isAllowed,
    isisPendingResendCode,
    isOpenSuccesDialog,
    handleCloseSuccessDialog,
  } = useVerifyResetCode({ type, onClose, openDialog, isCounterActive })

  return (
    <>
      <Dialog open={openDialog} onOpenChange={onClose}>
        <DialogContent>
          <DialogTitle>
            <HeaderPage title="enter_verification_code" description="enter_verification_code_desc" />
          </DialogTitle>
          <FormWrapper
            ref={formRef}
            defaultValues={defaultValues}
            onSubmit={onSubmit}
            schema={verifyResetCodeSchema()}
            className="flex flex-col gap-5 items-center mt-4"
          >
            <FormInputOTP name="otp" />

            <div className="flex justify-center items-center gap-1">
              {isCounterActive && <Counter counter={counter} />}
              <Button
                variant="secondary"
                type="button"
                onClick={handleResendCode}
                className="p-0 !w-fit text-secondary font- text-sm !bg-transparent"
                disabled={!isAllowed || isCounterActive || isPending}
              >
                {t('auth.click_resend_code')}
              </Button>
            </div>

            <Button type="submit" className="w-full" isLoading={isPending}>
              {t('button.verify')}
            </Button>
          </FormWrapper>
        </DialogContent>
      </Dialog>
      {type === 'register' && (
        <CustomDialog
          icon={<Image src={SuccessCheck} alt="success" width={56} height={56} />}
          openDialog={isOpenSuccesDialog}
          onClose={handleCloseSuccessDialog}
          title={t('dialog.success_register')}
          description={t('dialog.success_register_desc')}
          renderButton={
            <Button type="button" className="w-full" onClick={handleCloseSuccessDialog}>
              {t('dialog.start_now')}
            </Button>
          }
        />
      )}
    </>
  )
}
