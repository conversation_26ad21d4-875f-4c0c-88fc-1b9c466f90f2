@import 'tailwindcss';

@custom-variant dark (&:is(.dark *));

@utility container {
  margin-inline: auto;
  padding-inline: 2rem;
}

@media (min-width: 1440px) {
  .container {
    padding-inline: 3.5rem;
  }
}

@theme {
  --color-primary-50: var(--color-primary-50);
  --color-primary-100: var(--color-primary-100);
  --color-primary-200: var(--color-primary-200);
  --color-primary-300: var(--color-primary-300);
  --color-primary-400: var(--color-primary-400);
  --color-primary-500: var(--color-primary-500);
  --color-primary-600: var(--color-primary-600);
  --color-primary-700: var(--color-primary-700);
  --color-primary-800: var(--color-primary-800);
  --color-primary-900: var(--color-primary-900);
  --color-primary: var(--color-primary);

  --color-success-100: var(--color-success-100);
  --color-success-200: var(--color-success-200);
  --color-success-300: var(--color-success-300);
  --color-success-400: var(--color-success-400);
  --color-success-500: var(--color-success-500);
  --color-success-600: var(--color-success-600);
  --color-success-700: var(--color-success-700);
  --color-success-800: var(--color-success-800);
  --color-success-900: var(--color-success-900);
  --color-success: var(--color-success);

  --color-error-100: var(--color-error-100);
  --color-error-200: var(--color-error-200);
  --color-error-300: var(--color-error-300);
  --color-error-400: var(--color-error-400);
  --color-error-500: var(--color-error-500);
  --color-error-600: var(--color-error-600);
  --color-error-700: var(--color-error-700);
  --color-error-800: var(--color-error-800);
  --color-error-900: var(--color-error-900);
  --color-error: var(--color-error);

  --color-warning-100: var(--color-warning-100);
  --color-warning-200: var(--color-warning-200);
  --color-warning-300: var(--color-warning-300);
  --color-warning-400: var(--color-warning-400);
  --color-warning-500: var(--color-warning-500);
  --color-warning-600: var(--color-warning-600);
  --color-warning-700: var(--color-warning-700);
  --color-warning-800: var(--color-warning-800);
  --color-warning-900: var(--color-warning-900);
  --color-warning: var(--color-warning);

  --color-gray-50: var(--color-gray-50);
  --color-gray-100: var(--color-gray-100);
  --color-gray-200: var(--color-gray-200);
  --color-gray-300: var(--color-gray-300);
  --color-gray-400: var(--color-gray-400);
  --color-gray-500: var(--color-gray-500);
  --color-gray-600: var(--color-gray-600);
  --color-gray-700: var(--color-gray-700);
  --color-gray-800: var(--color-gray-800);
  --color-gray-900: var(--color-gray-900);
  --color-gray: var(--color-gray);

  --color-black: var(--color-black);
  --color-black-900: var(--color-black-900);
  --color-black-800: var(--color-black-800);
  --color-black-700: var(--color-black-700);
  --color-black-600: var(--color-black-600);
  --color-black-500: var(--color-black-500);
  --color-black-400: var(--color-black-400);
  --color-black-300: var(--color-black-300);
  --color-black-200: var(--color-black-200);
  --color-black-100: var(--color-black-100);
  --color-black-50: var(--color-black-50);

  --color-secondary: var(--color-secondary);
  --color-secondary-900: var(--color-secondary-900);
  --color-secondary-800: var(--color-secondary-800);
  --color-secondary-700: var(--color-secondary-700);
  --color-secondary-600: var(--color-secondary-600);
  --color-secondary-500: var(--color-secondary-500);
  --color-secondary-400: var(--color-secondary-400);
  --color-secondary-300: var(--color-secondary-300);
  --color-secondary-200: var(--color-secondary-200);
  --color-secondary-100: var(--color-secondary-100);
  --color-secondary-50: var(--color-secondary-50);

  --color-bg-page-light: var(--color-bg-page-light);
  --color-bg-form-light: var(--color-bg-form-light);
  --color-bg-dialog-light: var(--color-bg-dialog-light);
  --color-bg-input-light: var(--color-bg-input-light);
  --color-bg-action-light: var(--color-bg-action-light);
  --color-bg-error-light: var(--color-bg-error-light);
  --color-bg-layout-light: var(--color-bg-layout-light);
  --color-text-head-light: var(--color-text-head-light);
  --color-text-desc-light: var(--color-text-desc-light);
  --color-text-sub-light: var(--color-text-sub-light);
  --color-text-label-light: var(--color-text-label-light);
  --color-text-input-light: var(--color-text-input-light);
  --color-text-action-light: var(--color-text-action-light);
  --color-text-error-light: var(--color-text-error-light);
  --color-divider-light: var(--color-divider-light);
  --color-input-border-light: var(--color-input-border-light);
  --color-input-disabled-light: var(--color-input-disabled-light);
  --color-bg-input-disabled-light: var(--color-bg-input-disabled-light);

  --color-bg-page-dark: var(--color-bg-page-dark);
  --color-bg-dialog-dark: var(--color-bg-dialog-dark);
  --color-bg-form-dark: var(--color-bg-form-dark);
  --color-bg-input-dark: var(--color-bg-input-dark);
  --color-bg-action-dark: var(--color-bg-action-dark);
  --color-bg-error-dark: var(--color-bg-error-dark);
  --color-bg-layout-dark: var(--color-bg-layout-dark);
  --color-text-head-dark: var(--color-text-head-dark);
  --color-text-desc-dark: var(--color-text-desc-dark);
  --color-text-sub-dark: var(--color-text-sub-dark);
  --color-text-label-dark: var(--color-text-label-dark);
  --color-text-input-dark: var(--color-text-input-dark);
  --color-text-action-dark: var(--color-text-action-dark);
  --color-text-error-dark: var(--color-text-error-dark);
  --color-divider-dark: var(--color-divider-dark);
  --color-input-border-dark: var(--color-input-border-dark);
  --color-input-disabled-dark: var(--color-input-disabled-dark);
  --color-bg-input-disabled-dark: var(--color-bg-input-disabled-dark);
}

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
}

@utility text-balance {
  text-wrap: balance;
}



@layer base {
  html {
   --color-black: #000000;
  --color-black-900: #1A1A1A;
  --color-black-800: #353535;
  --color-black-700: #4D4D4D;
  --color-black-600: #666666;
  --color-black-500: #808080;
  --color-black-400: #999999;
  --color-black-300: #B3B3B3;
  --color-black-200: #CCCCCC;
  --color-black-100: #FAFAFA;
  --color-black-50: #F2F2F2;

  --color-gray: #101828;
  --color-gray-900: #252B37;
  --color-gray-800: #414651;
  --color-gray-700: #535862;
  --color-gray-600: #717680;
  --color-gray-500: #A4A7AE;
  --color-gray-400: #D5D7DA;
  --color-gray-300: #E9EAEB;
  --color-gray-200: #F5F5F5;
  --color-gray-100: #FAFAFA;
  --color-gray-50: #F7FDFD;


  --color-primary: #2C3E50;
  --color-primary-900: #415162;
  --color-primary-800: #566573;
  --color-primary-700: #6B7885;
  --color-primary-600: #808B96;
  --color-primary-500: #959FA7;
  --color-primary-400: #ABB2B9;
  --color-primary-300: #C0C5CB;
  --color-primary-200: #D5D8DC;
  --color-primary-100: #EAECED;
  --color-primary-50: #F4F5F6;


  --color-secondary: #E57F25;
  --color-secondary-900: #E88C3B;
  --color-secondary-800: #E57F25;
  --color-secondary-700: #EDA566;
  --color-secondary-600: #EFB27C;
  --color-secondary-500: #F2BF92;
  --color-secondary-400: #F5CCA8;
  --color-secondary-300: #F7D9BE;
  --color-secondary-200: #FAE5D3;
  --color-secondary-100: #FCF2E9;
  --color-secondary-50: #FEF9F4;


  --color-error: #7A271A;
  --color-error-900: #912018;
  --color-error-800: #B42318;
  --color-error-700: #D92D20;
  --color-error-600: #F04438;
  --color-error-500: #F97066;
  --color-error-400: #FDA29B;
  --color-error-300: #FECDCA;
  --color-error-200: #FEE4E2;
  --color-error-100: #FEF3F2;
  --color-error-50: #FFFBFA;


  --color-warning: #7A2E0E;
  --color-warning-900: #93370D;
  --color-warning-800: #B54708;
  --color-warning-700: #DC6803;
  --color-warning-600: #F79009;
  --color-warning-500: #FDB022;
  --color-warning-400: #FEC84B;
  --color-warning-300: #FEDF89;
  --color-warning-200: #FEF0C7;
  --color-warning-100: #FFFAEB;
  --color-warning-50: #FFFCF5;


  --color-success: #054F31;
  --color-success-900: #05603A;
  --color-success-800: #027A48;
  --color-success-700: #039855;
  --color-success-600: #12B76A;
  --color-success-500: #32D583;
  --color-success-400: #6CE9A6;
  --color-success-300: #A6F4C5;
  --color-success-200: #D1FADF;
  --color-success-100: #ECFDF3;
  --color-success-50: #F6FEF9;

  --color-bg-page-light: #FFFFFF;
  --color-bg-dialog-light: #FBFBFB;
  --color-bg-input-light: #FFFFFF;
  --color-bg-form-light: #F4F5F6;
  --color-bg-input-disabled-light: #EBEBEB;
  --color-bg-action-light: #EAECED;
  --color-bg-error-light: #FEE4E2;
  --color-bg-layout-light: #2C3E50;
  --color-text-head-light: #333333;
  --color-text-desc-light: #666666;
  --color-text-sub-light: #808080;
  --color-text-label-light: #333333;
  --color-text-input-light: #4D4D4D;
  --color-text-action-light: #2C3E50;
  --color-text-error-light: #F04438;
  --color-divider-light: #CCCCCC;
  --color-input-border-light: #D8DADC;

  --color-bg-page-dark: #111827;
  --color-bg-form-dark: #1F2937 ;
  --color-bg-dialog-dark: #111827;
  --color-bg-input-dark: #374151;
  --color-bg-input-disabled-dark: #192333;
  --color-bg-action-dark: #E57F25;
  --color-bg-error-dark: #F04438;
  --color-bg-layout-dark: #1F2937;
  --color-text-head-dark: #E57F25;
  --color-text-desc-dark: #9CA3AF;
  --color-text-sub-dark: #F4F5F6;
  --color-text-label-dark: #9CA3AF;
  --color-text-input-dark: #FFFFFF;
  --color-text-action-dark: #FFFFFF;
  --color-text-error-dark: #FFFFFF;
  --color-divider-dark: #374151;
  --color-input-border-dark: #374151;
  }
}

body {
  background-color: var(--color-bg-page-light);
}

.rdp-root {
  --rdp-accent-color: var(--color-primary-500) !important;
  --rdp-accent-background-color: var(--color-primary-500) !important;
}

.rdp-day.rdp-today.rdp-selected:not(.rdp-range_middle) {
  @apply text-white;
}

.rdp-day.rdp-today.rdp-selected.rdp-range_middle {
  @apply text-black}

html[class="dark"] {
  body {
    background-color: var(--color-bg-page-dark);
  }
}

/* width */
::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

/* Track */
::-webkit-scrollbar-track {
  border-radius: 7px;
  overflow: hidden;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: var(--color-gray-500);
  border-radius: 10px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: var(--color-gray-500);
}

/*
   Animation example, for spinners
*/
.animate-spin {
  -moz-animation: spin 1.5s infinite linear;
  -o-animation: spin 1.5s infinite linear;
  -webkit-animation: spin 1.5s infinite linear;
  animation: spin 1.5s infinite linear;
  display: inline-block;
}
@-moz-keyframes spin {
  0% {
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -moz-transform: rotate(359deg);
    -o-transform: rotate(359deg);
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
@-webkit-keyframes spin {
  0% {
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -moz-transform: rotate(359deg);
    -o-transform: rotate(359deg);
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
@-o-keyframes spin {
  0% {
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -moz-transform: rotate(359deg);
    -o-transform: rotate(359deg);
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
@-ms-keyframes spin {
  0% {
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -moz-transform: rotate(359deg);
    -o-transform: rotate(359deg);
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
@keyframes spin {
  0% {
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -moz-transform: rotate(359deg);
    -o-transform: rotate(359deg);
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}