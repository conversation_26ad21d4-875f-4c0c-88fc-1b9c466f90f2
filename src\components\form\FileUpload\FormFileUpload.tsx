import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { File, Plus, X } from 'lucide-react'
import Image from 'next/image'
import { FormField, FormItem, FormMessage } from '../../ui/form'
import useFormFileUpload, { FormFileUploadProps } from './useFormFileUpload'

export interface IFormImageUploadProps extends FormFileUploadProps {
  children?: React.ReactNode
  className?: string
}

interface IFileResponse {
  id?: string | number
  name: string
  path: string
  type?: string
}

interface FilesPreviewProps {
  selectedFiles: (File | IFileResponse | string)[] | File | IFileResponse | string
  removeImage: (index: number, url?: string) => void
}

const FormFileUpload = ({
  name,
  multiple,
  maxSize = 10,
  maxLength,
  accept = 'image/*',
  className,
  children,
}: IFormImageUploadProps) => {
  const {
    selectedFiles,
    handleFileChange,
    removeImage,
    handleDragOver,
    handleDragLeave,
    handleDrop,
    isDragging,
    fileInputRef,
    errors,
    control,
    t,
  } = useFormFileUpload({
    name,
    multiple,
    maxSize,
    maxLength,
    accept,
  })

  return (
    <FormField
      name={name}
      control={control}
      render={() => (
        <>
          <div
            className={cn(
              'border border-input-border-light dark:border-input-border-dark rounded-lg transition-colors shadow-2xs bg-transparent overflow-hidden',
              isDragging && 'border-primary',
              errors[name] && 'bg-red-50 border-error-700 dark:border-error-500',
              className
            )}
          >
            <label htmlFor={`image-upload-${name}`} className="flex flex-col gap-2 w-full">
              <FormItem>
                <div onDrop={handleDrop} onDragOver={handleDragOver} onDragLeave={handleDragLeave}>
                  {selectedFiles.length === 0 && children && children}

                  {selectedFiles.length === 0 && !children && (
                    <span className="text-secondary flex items-center justify-center gap-2 font-medium min-h-[50px] w-full bg-bg-input-light dark:bg-bg-input-dark">
                      <Plus size={18} />
                      {t('label.add_image')}
                    </span>
                  )}

                  <input
                    multiple={multiple}
                    ref={fileInputRef}
                    id={`image-upload-${name}`}
                    type="file"
                    accept={accept}
                    onChange={handleFileChange}
                    className="hidden"
                  />
                </div>
              </FormItem>
            </label>

            <FilesPreview selectedFiles={selectedFiles} removeImage={removeImage} />
          </div>
          <FormMessage />
        </>
      )}
    />
  )
}

export default FormFileUpload

const FilesPreview = ({ selectedFiles, removeImage }: FilesPreviewProps) => {
  if (!selectedFiles) return <></>

  const filesAsArray = Array.isArray(selectedFiles) ? selectedFiles : [selectedFiles]

  return (
    <div className={cn('bg-bg-input-light dark:bg-bg-input-dark', filesAsArray.length !== 0 && 'p-4')}>
      {filesAsArray.map((file, index) => {
        const isExistingFile = typeof file === 'object' && file !== null && 'path' in file
        const fileUrl =
          typeof file === 'string'
            ? file
            : isExistingFile
              ? (file as IFileResponse).path
              : URL.createObjectURL(file as File)

        const fileType =
          typeof file === 'string'
            ? 'image/'
            : isExistingFile
              ? ((file as IFileResponse).type ?? 'image/')
              : (file as File).type

        return (
          <div
            key={isExistingFile ? `${(file as IFileResponse).id}-${(file as IFileResponse).name}` : fileUrl}
            className="group rounded-md relative max-w-[120px] mx-auto"
          >
            {fileType.startsWith('image/') ? (
              <Image
                alt={(file as any).name || 'image'}
                src={fileUrl}
                className="object-cover object-center w-full h-full rounded-md"
                width={60}
                height={60}
              />
            ) : (
              <File size={24} />
            )}

            <Button
              type="button"
              variant="destructive"
              onClick={() => removeImage(index, isExistingFile ? undefined : fileUrl)}
              className="absolute top-[-4px] end-[-4px] opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none group-hover:pointer-events-auto !p-0 bg-bg-error-dark rounded-full w-6 h-6 flex items-center justify-center"
            >
              <X size={24} />
            </Button>
          </div>
        )
      })}
    </div>
  )
}
