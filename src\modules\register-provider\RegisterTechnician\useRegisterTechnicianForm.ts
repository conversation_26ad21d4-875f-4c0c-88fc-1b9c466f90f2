import { IFormWrapper } from '@/components/core/FormWrapper'
import useApi from '@/hooks/useApi'
import { useLocalizedRouter } from '@/hooks/useLocalizedRouter'
import { Routes } from '@/routes/routes'
import generateBody from '@/utils/generateBody'
import { getAddressFromCoordinates } from '@/utils/getAddressFormCoordinates'
import { useTranslations } from 'next-intl'
import { useEffect, useRef, useState } from 'react'
import { IRegisterTechnicianForm } from '../types'

const useRegisterTechnicianForm = ({ userData }: { userData: IRegisterTechnicianForm }) => {
  const t = useTranslations()
  const formRef = useRef<IFormWrapper>(null)

  const { push } = useLocalizedRouter()
  const [isOpenSuccessDialog, setIsOpenSuccessDialog] = useState(false)
  const { action, isPending } = useApi({
    path: '/user/profile/upgrade-to-technician',
    method: 'POST',
    handleSuccess: false,
    onSuccess: async () => {
      setIsOpenSuccessDialog(true)
    },
  })

  const handleCloseSuccessDialog = () => {
    setIsOpenSuccessDialog(false)
    push(Routes.HOME)
  }

  useEffect(() => {
    const fetchAddress = async () => {
      try {
        if (userData?.location?.lat && userData?.location?.lng) {
          const { address } = await getAddressFromCoordinates(userData.location.lat, userData.location.lng)

          formRef.current?.setValues({
            ...userData,
            location: address || null,
          })
        } else {
          formRef.current?.setValues({
            ...userData,
          })
        }
      } catch (error) {
        console.error('Error fetching address:', error)
      }
    }

    fetchAddress()
  }, [userData])

  const handleSubmit = (payload: IRegisterTechnicianForm) => {
    const formdata = generateBody(payload)
    payload.location_lat && formdata.append('location[lat]', payload.location_lat.toString())
    payload.location_lng && formdata.append('location[lng]', payload.location_lng.toString())
    action(formdata)
  }

  return {
    t,
    formRef,
    isPending,
    handleSubmit,
    handleCloseSuccessDialog,
    isOpenSuccessDialog,
  }
}

export default useRegisterTechnicianForm
