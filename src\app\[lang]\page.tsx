import { LocaleToggle } from '@/components/core/LocaleToggle'
import ThemeSwitch from '@/components/shared/ThemeSwitch'
import { Button } from '@/components/ui/button'
import { getServerAuthSession } from '@/config/auth'
import { cn } from '@/lib/utils'
import { handleLogout } from '@/utils/handleLogout'
import { getTranslations } from 'next-intl/server'

export const dynamic = 'force-dynamic'
export default async function Home() {
  const t = await getTranslations()
  const session = await getServerAuthSession()
  return (
    <main className="flex min-h-screen  items-center justify-center">
      <ThemeSwitch />

      <LocaleToggle />
      {session && (
        <Button
          variant="ghost"
          size="icon"
          className={cn(
            'h-8 w-8 justify-start flex items-center gap-3 px-3 py-2 rounded-lg text-lg font-medium transition-colors',
            'text-primary-02 hover:bg-gray-100 hover:text-primary-01'
          )}
          onClick={handleLogout}
        >
          {t('navbar.logout')}
        </Button>
      )}
    </main>
  )
}
