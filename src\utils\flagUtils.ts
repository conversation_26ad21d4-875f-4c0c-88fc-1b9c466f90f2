/**
 * Utility functions for handling country flags and emojis
 */

/**
 * Check if a string is a country code (2-3 uppercase letters)
 * @param str - The string to check
 * @returns boolean indicating if the string is a country code
 */
export const isCountryCode = (str: string): boolean => {
  return /^[A-Z]{2,3}$/.test(str)
}

/**
 * Convert country code to Unicode flag emoji
 * @param countryCode - The country code (e.g., "SA", "US", "GB")
 * @returns Unicode flag emoji or original string if not a country code
 */
export const countryCodeToEmoji = (countryCode: string): string => {
  if (!isCountryCode(countryCode)) return countryCode

  // Convert country code to Unicode flag emoji
  const codePoints = countryCode
    .toUpperCase()
    .split('')
    .map((char) => 127397 + char.charCodeAt(0))

  return String.fromCodePoint(...codePoints)
}

/**
 * Get flag URL from country code using flagcdn.com
 * @param countryCode - The country code (e.g., "SA", "US", "GB")
 * @param size - The size of the flag (w20, w40, w80, w160, w320, w640, w1280)
 * @returns URL to the flag image
 */
export const getFlagUrl = (countryCode: string, size: string = 'w20'): string => {
  return `https://flagcdn.com/${size}/${countryCode.toLowerCase()}.png`
}

/**
 * Check if a string is already a Unicode emoji
 * @param str - The string to check
 * @returns boolean indicating if the string contains emoji
 */
export const isEmoji = (str: string): boolean => {
  const emojiRegex = /[\u{1F1E6}-\u{1F1FF}]{2}/u
  return emojiRegex.test(str)
}

/**
 * Normalize emoji/country code to ensure consistent display
 * @param emojiOrCode - Either a country code or Unicode emoji
 * @returns Unicode flag emoji
 */
export const normalizeFlag = (emojiOrCode: string): string => {
  if (isEmoji(emojiOrCode)) {
    return emojiOrCode
  }

  if (isCountryCode(emojiOrCode)) {
    return countryCodeToEmoji(emojiOrCode)
  }

  return emojiOrCode
}
