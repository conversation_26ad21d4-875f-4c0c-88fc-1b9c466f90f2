'use client'

import { ThemedImage } from '@/components/ui/themed-image'
import LogoLight from '/public/icons/logo.svg'
import LogoDark from '/public/icons/logo_dark.svg'

/**
 * Logo component that automatically switches between light and dark themes
 * Uses ThemedImage to prevent hydration mismatches
 */
export default function LogoImg() {
  return <ThemedImage lightSrc={LogoLight} darkSrc={LogoDark} alt="Company logo" width={130} height={120} />
}
