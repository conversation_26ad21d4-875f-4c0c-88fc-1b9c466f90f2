import { CountryDropdown } from '@/components/ui/country-dropdown'
import { useFormContext } from 'react-hook-form'
import { Input } from '@/components/ui/input'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { useEffect } from 'react'
import { PhoneData } from '@/types/auth'
import { cn } from '@/lib/utils'

interface FormPhoneInputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange' | 'prefix' | 'suffix'> {
  phoneName: string
  label?: string
  placeholder?: string
  required?: boolean
  containerClassName?: string
  disabledCountry?: boolean
}

export function FormPhoneInput({
  phoneName,
  label,
  placeholder,
  required,
  containerClassName,
  disabledCountry = false,
  ...props
}: FormPhoneInputProps) {
  const { control, setValue, watch } = useFormContext()

  const defaultCountry = {
    alpha2: 'SA',
    alpha3: 'SAU',
    countryCallingCodes: ['+966'],
    currencies: ['SAR'],
    emoji: '🇸🇦',
    ioc: 'KSA',
    languages: ['ara'],
    name: 'Saudi Arabia',
    status: 'assigned',
  }

  const selectedCountry = watch(`${phoneName}.country_code`)
  const phoneIdentifier = watch(`${phoneName}.identifier`)

  // Initialize phone object structure when component mounts
  useEffect(() => {
    const currentPhoneValue = watch(phoneName)
    if (!currentPhoneValue || typeof currentPhoneValue === 'string') {
      setValue(phoneName, {
        identifier: '',
        country_code: '966', // Default Saudi Arabia code
      })
    }
  }, [phoneName, setValue, watch])

  return (
    <FormField
      control={control}
      name={`${phoneName}.identifier`}
      render={({ field }) => (
        <FormItem>
          {label && (
            <FormLabel>
              {label} {required && <span className="text-error-700 dark:text-error-500">*</span>}
            </FormLabel>
          )}
          <FormControl>
            <div
              className={cn(
                `flex border border-input-border-light  dark:border-input-border-dark rounded-[10px] h-[56px]`,
                containerClassName
              )}
              dir="ltr"
            >
              <CountryDropdown
                slim
                onChange={(country) => {
                  const countryCode = country.countryCallingCodes[0].replace('+', '')
                  setValue(phoneName, {
                    identifier: phoneIdentifier || '',
                    country_code: countryCode,
                  })
                }}
                defaultValue={defaultCountry}
                disabled={disabledCountry}
                triggerClassName={props.disabled ? 'bg-bg-input-disabled-light dark:bg-bg-input-dark' : undefined}
              />
              <Input
                slim
                type="number"
                placeholder={placeholder}
                {...field}
                value={phoneIdentifier || ''}
                containerClassName="w-full h-full rounded-s-none border-none"
                className="h-full"
                onChange={(e) => {
                  const newValue = {
                    identifier: e.target.value,
                    country_code: selectedCountry || '966',
                  }
                  setValue(phoneName, newValue)
                }}
                {...props}
              />
            </div>
          </FormControl>

          <FormMessage />
        </FormItem>
      )}
    />
  )
}
