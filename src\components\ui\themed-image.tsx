'use client'

import { useTheme } from 'next-themes'
import Image, { ImageProps, StaticImageData } from 'next/image'
import { useHasMounted } from '@/hooks/useHasMounted'
import { ComponentType } from 'react'

/**
 * Props for ThemedImage component
 */
export interface ThemedImageProps extends Omit<ImageProps, 'src'> {
  /** Image source for light theme */
  lightSrc: string | StaticImageData
  /** Image source for dark theme */
  darkSrc: string | StaticImageData
  /** Optional fallback component to render during SSR */
  fallback?: ComponentType<{ width?: number | string; height?: number | string; className?: string }>
  /** Whether to render nothing during SSR (default: false) */
  hideOnSSR?: boolean
}

/**
 * ThemedImage - Automatically switches image based on current theme
 *
 * This component prevents hydration mismatches by:
 * 1. Detecting if component is mounted on client
 * 2. Rendering fallback/nothing during SSR
 * 3. Rendering correct themed image after hydration
 *
 * @example
 * <ThemedImage
 *   lightSrc={EditIconLight}
 *   darkSrc={EditIconDark}
 *   alt="Edit"
 *   width={20}
 *   height={20}
 * />
 */
export const ThemedImage = ({
  lightSrc,
  darkSrc,
  fallback: Fallback,
  hideOnSSR = false,
  width,
  height,
  className,
  ...props
}: ThemedImageProps) => {
  const { theme, resolvedTheme } = useTheme()
  const hasMounted = useHasMounted()

  // During SSR or before hydration
  if (!hasMounted) {
    if (hideOnSSR) {
      return null
    }

    if (Fallback) {
      return <Fallback width={width} height={height} className={className} />
    }

    // Render invisible placeholder to maintain layout
    return (
      <span
        style={{
          display: 'inline-block',
          width: typeof width === 'number' ? `${width}px` : width,
          height: typeof height === 'number' ? `${height}px` : height,
        }}
        className={className}
        aria-hidden="true"
      />
    )
  }

  // After hydration - render themed image
  const currentTheme = theme || resolvedTheme
  const imageSrc = currentTheme === 'dark' ? darkSrc : lightSrc

  return <Image src={imageSrc} width={width} height={height} className={className} {...props} alt={props.alt} />
}

/**
 * Default placeholder component for ThemedImage
 */
export const ThemedImagePlaceholder = ({
  width,
  height,
  className,
}: {
  width?: number | string
  height?: number | string
  className?: string
}) => {
  return (
    <div
      className={className}
      style={{
        width: typeof width === 'number' ? `${width}px` : width,
        height: typeof height === 'number' ? `${height}px` : height,
        backgroundColor: 'transparent',
      }}
      aria-hidden="true"
    />
  )
}
