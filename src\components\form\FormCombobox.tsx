// context
import { useFormWrapperContext } from '@/components/core/FormWrapper'
import { useFormContext } from 'react-hook-form'

// ui components
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Combobox, ComboboxProps } from '@/components/ui/combobox'

interface FormComboboxProps<T> extends Omit<ComboboxProps<T>, 'onChange'> {
  name: string
  label?: string
  onChange?: (value: T) => void
  required?: boolean
}

export function FormCombobox<T>({
  name,
  label,
  data,
  valueKey,
  labelKey,
  onChange,
  onSearch, // if onSearch is provided, the combobox will be controlled and you can add server search you want
  onToggle,
  required,
  ...props
}: FormComboboxProps<T>) {
  const { control } = useFormContext()
  const { errors } = useFormWrapperContext()

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          {label && (
            <FormLabel>
              {label} {required && <span className="text-error-700 dark:text-error-500">*</span>}
            </FormLabel>
          )}
          <FormControl>
            <Combobox
              {...props}
              data={data}
              valueKey={valueKey}
              labelKey={labelKey}
              aria-invalid={!!errors[name]}
              hasError={!!errors[name]}
              value={field.value ? data.find((item) => item[valueKey] === field.value) || null : null}
              onChange={(newValue) => {
                // Send only the value (id) instead of the whole object
                const valueToSend = newValue ? newValue[valueKey] : null
                field.onChange(valueToSend)
                onChange && onChange(newValue as T)
              }}
              {...(!!onSearch && { onSearch })}
              {...(!!onToggle && { onToggle })}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}
