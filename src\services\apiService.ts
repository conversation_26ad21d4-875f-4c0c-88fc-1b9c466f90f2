'use server'

import { getServerAuthSession } from '@/config/auth'
import { env } from '@/config/environment'
import { i18n, Locale } from '@/i18n-config'
import { getLocale } from 'next-intl/server'

export interface ApiServiceProps extends RequestInit {
  path: string
  searchParams?: Record<string, any>
  locale?: string
  revalidate?: number | false
  enabled?: boolean
}

export async function apiService({ path, searchParams, revalidate, enabled = true, ...props }: ApiServiceProps) {
  try {
    if (!enabled) return

    const user = await getServerAuthSession()
    let locale: Locale = i18n?.defaultLocale ?? 'en'
    try {
      const maybeLocale = await getLocale()
      if (typeof maybeLocale === 'string' && maybeLocale) locale = maybeLocale as Locale
    } catch (e) {
      // don't throw — log and fallback
    }

    // Validate BASE_API environment variable
    if (!env.BASE_API) {
      console.error('❌ BASE_API environment variable is not set')
    }

    const headers = {
      ...props.headers,
      'Accept-Language': locale || 'en',
      Accept: 'application/json',
      ...(user?.user?.data?.token && { Authorization: `Bearer ${user?.user.data.token}` }),
    }

    const BASE_URL = env.BASE_API || ''
    const urlSearchParams = new URLSearchParams(searchParams)
    const url = `${BASE_URL}${path}${urlSearchParams.size ? `?${urlSearchParams}` : ''}`

    const res = await fetch(url, {
      ...props,
      method: props.method || 'GET',
      headers,
      next: {
        tags: [path, urlSearchParams.toString() || ''],
        ...(revalidate !== undefined && { revalidate }),
        ...props.next,
      },
    })

    // Handle success
    if (res.ok) {
      const jsonRes = await res.json().catch(() => ({}))
      return {
        status: true,
        statusCode: res.status,
        data: jsonRes,
        message: jsonRes.message,
      }
    } else {
      // Handle errors
      let errorData: any
      try {
        errorData = await res.json()
        console.log(errorData)
      } catch {
        errorData = { message: res.statusText }
        console.log(errorData)
      }

      return {
        status: false,
        statusCode: res.status,
        data: null,
        message: errorData.message || res.statusText,
        errors: errorData.errors || errorData,
      }
    }
  } catch (e: any) {
    console.error('🚀 ~ apiService ~ unexpected error', path, e)
    return {
      status: false,
      statusCode: 500,
      data: null,
      message: e?.message || 'Unexpected error occurred',
      errors: e,
    }
  }
}
