'use client'

import { <PERSON>Wrapper } from '@/components/core/FormWrapper'
import { FormInput } from '@/components/form'
import { Button } from '@/components/ui/button'
import { schema } from './form.validation'
import useRegisterSupplierForm from './useRegisterSupplierForm'
import FormFileUpload from '@/components/form/FileUpload/FormFileUpload'
import { FormRadioInput } from '@/components/form/FormRadioInput'
import { IRegisterSupplierForm } from '../types'
import useApi from '@/hooks/useApi'
import LocationInput from '../components/LocationInput'
import CustomDialog from '@/components/shared/CustomDialog'
import { FormMultiSelect } from '@/components/form/FormMultiSelect'
import FormHeader from '../components/FormHeader'

const defaultValues: IRegisterSupplierForm = {
  supplier_type: 'wholesale',
  company_name: '',
  company_email: '',
  tax_number: '',
  commercial_register: '',
  categories: null as unknown as number[],
  id: null as unknown as number,
  name: '',
  email: '',
  country_code: '',
  phone: '',
  image: '',
}

const RegisterSupplierForm = ({ userData }: { userData: IRegisterSupplierForm }) => {
  const { t, isPending, handleSubmit, formRef, isOpenSuccessDialog, handleCloseSuccessDialog } =
    useRegisterSupplierForm({ userData: userData })
  const { state: categories } = useApi<{ data: IDDl[] }>({
    path: '/ddl/categories',
    method: 'GET',
    intermediate: true,
  })
  const RegisterSupplierFormSchema = schema(t, userData)
  return (
    <div className="bg-bg-form-light dark:bg-bg-form-dark p-6 rounded-[10px] w-full max-w-[750px] mx-auto">
      <FormHeader />
      <FormWrapper
        ref={formRef}
        schema={RegisterSupplierFormSchema}
        defaultValues={defaultValues}
        onSubmit={handleSubmit}
      >
        <div className="mb-3">
          <FormRadioInput
            name="type"
            label={t('label.supplier_type')}
            options={[
              { label: t('label.wholesale_supplier'), value: 'wholesale' },
              { label: t('label.retail_supplier'), value: 'retail' },
            ]}
          />
        </div>
        <div className="space-y-2">
          <FormInput
            containerClassName="flex"
            name="company_name"
            label={t('label.company_name')}
            placeholder={t('label.enter_company_name')}
            required
          />
          <FormInput
            containerClassName="flex"
            name="company_email"
            label={t('label.company_email')}
            placeholder={t('label.enter_company_email')}
            type="email"
            required
          />
          <FormInput
            containerClassName="flex"
            name="username"
            label={t('label.username')}
            placeholder={t('label.enter_username')}
            required
            disabled={!!userData?.username}
          />
          <FormInput
            name="commercial_register"
            label={t('label.commercial_register')}
            placeholder={t('label.enter_commercial_registration')}
            required
            type="number"
          />

          <FormInput
            name="tax_number"
            label={t('label.tax_number')}
            placeholder={t('label.enter_tax_number')}
            required
            type="number"
          />
          <FormInput
            name="bank_name"
            label={t('label.bank_name')}
            placeholder={t('label.enter_bank_name')}
            required
            disabled={!!userData?.bank_name}
          />

          <FormInput
            name="bank_account_number"
            type="number"
            label={t('label.account_number')}
            placeholder={t('label.enter_account_number')}
            required
            disabled={!!userData?.bank_account_number}
          />
          <FormInput
            name="iban"
            label={t('label.iban_number')}
            placeholder={t('label.enter_iban')}
            required
            disabled={!!userData?.iban}
          />
          <FormInput
            name="phone"
            label={t('label.phone_number')}
            placeholder={t('label.enter_phone_number')}
            required
            disabled={!!userData?.phone}
          />

          <LocationInput {...userData} />
          <FormMultiSelect
            name="categories"
            label={t('label.supplier_specialization')}
            placeholder={t('label.choose_supplier_specialization')}
            data={categories.data?.data?.data || []}
            valueKey="id"
            labelKey="name"
            maxCount={3}
            required
          />
          <FormInput
            name="email"
            label={t('label.email')}
            placeholder={t('label.enter_email')}
            disabled={!!userData?.email}
          />
        </div>

        {userData && !userData.image && <FormFileUpload name="image" className="mt-4" />}

        <Button className="mt-9 mb-3" type="submit" isLoading={isPending}>
          {t('label.submit_request')}
        </Button>
      </FormWrapper>
      <CustomDialog
        openDialog={isOpenSuccessDialog}
        onClose={handleCloseSuccessDialog}
        title={t('dialog.request_submitted_successfully')}
        description={t('dialog.data_under_review')}
        renderButton={
          <Button type="button" className="w-full" onClick={handleCloseSuccessDialog}>
            {t('dialog.back')}
          </Button>
        }
      />
    </div>
  )
}

export default RegisterSupplierForm
